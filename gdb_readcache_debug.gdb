# GDB调试脚本：ReadCache数据块读取功能
# 使用方法: gdb -x gdb_readcache_debug.gdb ./opengemini_debug

# 设置GDB环境
set confirm off
set pagination off
set print pretty on
set print array on
set print array-indexes on

# 设置Go运行时环境
set environment GODEBUG=gctrace=1
set environment GOMAXPROCS=4

echo \n=== ReadCache数据块读取调试会话开始 ===\n

# 1. 核心函数断点设置
echo 设置核心函数断点...\n

# 主入口函数断点
break github.com/openGemini/openGemini/engine/immutable.(*tsspFileReader).ReadDataBlock
commands
    echo \n=== 进入ReadDataBlock ===
    echo 参数信息:
    print offset
    print size  
    print ioPriority
    echo 缓存启用状态:
    print fileops.ReadDataCacheEn
    call $1.cacheEnable(ioPriority)
    echo ========================\n
    continue
end

# PageCacheReader.Read断点
break github.com/openGemini/openGemini/engine/immutable.(*PageCacheReader).Read
commands
    echo \n=== 进入PageCacheReader.Read ===
    echo 读取参数:
    print offset
    print size
    echo 页缓存读取器状态:
    print $1.init
    print $1.startOffset
    print $1.endOffset
    echo ================================\n
    continue
end

# ReadFixPageSize断点 - 固定页大小读取
break github.com/openGemini/openGemini/engine/immutable.(*PageCacheReader).ReadFixPageSize
commands
    echo \n=== 进入ReadFixPageSize ===
    echo 读取范围:
    print offset
    print size
    echo 页面计算:
    # 这里会调用GetCachePageIdsAndOffsets
    echo 即将计算页面ID和偏移量...
    echo ===========================\n
    continue
end

# ReadSinglePage断点 - 单页读取核心
break github.com/openGemini/openGemini/engine/immutable.(*PageCacheReader).ReadSinglePage
commands
    echo \n=== 进入ReadSinglePage ===
    echo 缓存键: 
    print cacheKey
    echo 页面偏移和大小:
    print pageOffset
    print pageSize
    echo 检查缓存命中状态...
    echo ===========================\n
    continue
end

# 2. 缓存相关断点
echo 设置缓存管理断点...\n

# 缓存释放断点
break github.com/openGemini/openGemini/engine/immutable.(*tsspFileReader).UnrefCachePage
commands
    echo \n=== 缓存页面释放 ===
    if cachePage != 0
        echo 释放缓存页面，地址:
        print cachePage
    else
        echo 缓存页面为空，无需释放
    end
    echo ===================\n
    continue
end

# 3. 错误处理断点
echo 设置错误处理断点...\n

# 读取失败断点 - 在函数返回处设置
break github.com/openGemini/openGemini/engine/immutable.(*PageCacheReader).ReadSinglePage
commands
    silent
    continue
end

# 4. 性能监控断点
echo 设置性能监控断点...\n

# 统计信息更新断点
break github.com/openGemini/openGemini/lib/statisticsPusher/statistics.(*IOStatistics).AddReadDataCount
commands
    echo \n=== IO统计更新 ===
    echo 读取数据量:
    print size
    echo ================\n
    continue
end

# 5. 自定义调试函数
define print_cache_status
    echo \n=== 缓存状态信息 ===
    # 这里可以添加更多缓存状态的打印逻辑
    echo 当前缓存实例状态...
    echo ===================\n
end

define trace_read_path
    echo \n=== 读取路径跟踪 ===
    echo 开始跟踪数据读取路径...
    # 启用更详细的跟踪
    set trace-commands on
    echo ===================\n
end

# 6. 高级调试功能
define analyze_cache_hit_rate
    echo \n=== 缓存命中率分析 ===
    # 设置计数器（需要在调试过程中手动更新）
    set $cache_hits = 0
    set $cache_misses = 0
    echo 缓存命中次数: $cache_hits
    echo 缓存未命中次数: $cache_misses
    if $cache_hits + $cache_misses > 0
        set $hit_rate = $cache_hits * 100 / ($cache_hits + $cache_misses)
        printf "缓存命中率: %d%%\n", $hit_rate
    end
    echo ========================\n
end

define trace_memory_usage
    echo \n=== 内存使用跟踪 ===
    echo 当前Go运行时内存统计:
    # 这需要在运行时调用Go的内存统计函数
    echo 提示: 使用runtime.ReadMemStats()查看详细内存信息
    echo =====================\n
end

define debug_page_calculation
    echo \n=== 页面计算调试 ===
    echo 当前读取请求的页面分析:
    echo 请在GetCachePageIdsAndOffsets函数中查看:
    echo - 起始页面ID
    echo - 页面偏移量
    echo - 页面数量
    echo ========================\n
end

# 7. 启动提示
echo \n调试断点设置完成！
echo
echo 可用的调试命令:
echo   - print_cache_status: 打印缓存状态
echo   - trace_read_path: 启用读取路径跟踪
echo   - analyze_cache_hit_rate: 分析缓存命中率
echo   - trace_memory_usage: 跟踪内存使用
echo   - debug_page_calculation: 调试页面计算
echo   - info breakpoints: 查看所有断点
echo   - continue: 继续执行
echo   - step: 单步执行
echo   - next: 下一行
echo
echo 现在可以运行程序开始调试...
echo 使用 'run' 命令启动程序
echo
